import axios, {
  type AxiosError,
  type AxiosInstance,
  type AxiosRequestConfig,
  type AxiosResponse,
} from 'axios'
import { toast } from '../toast'

// API configuration
const API_BASE_URL =
  (typeof window !== 'undefined' && (window as any).__API_BASE_URL__) ||
  (typeof process !== 'undefined' && process.env?.NEXT_PUBLIC_API_URL) ||
  'http://localhost:3000/api/v1'
const API_TIMEOUT = 30000 // 30 seconds
const MAX_RETRIES = 3
const RETRY_DELAY = 1000 // 1 second

// Token storage keys
const ACCESS_TOKEN_KEY = 'ld_access_token'
const REFRESH_TOKEN_KEY = 'ld_refresh_token'
const USER_KEY = 'ld_user'

// API Error types
export interface ApiError {
  message: string
  statusCode: number
  error?: string
  details?: any
  timestamp?: string
  path?: string
  traceId?: string
}

// Error categories for better handling
export enum ErrorCategory {
  NETWORK = 'NETWORK',
  AUTHENTICATION = 'AUTHENTICATION',
  AUTHORIZATION = 'AUTHORIZATION',
  VALIDATION = 'VALIDATION',
  SERVER = 'SERVER',
  TIMEOUT = 'TIMEOUT',
  UNKNOWN = 'UNKNOWN',
}

// Enhanced error with category
export interface CategorizedError extends ApiError {
  category: ErrorCategory
  retryable: boolean
  userMessage: string
}

// Retry configuration
export interface RetryConfig {
  maxRetries: number
  baseDelay: number
  maxDelay: number
  retryCondition: (error: AxiosError) => boolean
}

// Request metadata for tracking
export interface RequestMetadata {
  startTime: number
  retryCount: number
  endpoint: string
  method: string
}

// Token types
export interface TokenPair {
  accessToken: string
  refreshToken: string
  expiresIn: number
}

// User type
export interface User {
  id: string
  email: string
  name: string
  departmentId?: string
  roleId?: string
  roles?: Array<{
    id: string
    name: string
    permissions: string[]
  }>
  department?: {
    id: string
    name: string
  }
}

// API Response wrapper
export interface ApiResponse<T = any> {
  data: T
  message?: string
  pagination?: {
    page: number
    limit: number
    total: number
    totalPages: number
  }
}

class ApiClient {
  private client: AxiosInstance
  private refreshPromise: Promise<TokenPair> | null = null
  private defaultRetryConfig: RetryConfig

  constructor() {
    this.client = axios.create({
      baseURL: API_BASE_URL,
      timeout: API_TIMEOUT,
      headers: {
        'Content-Type': 'application/json',
        'X-Client-Version': '1.0.0',
      },
    })

    this.defaultRetryConfig = {
      maxRetries: MAX_RETRIES,
      baseDelay: RETRY_DELAY,
      maxDelay: 10000,
      retryCondition: (error: AxiosError) => {
        // Retry on network errors, timeouts, and 5xx server errors
        return (
          !error.response ||
          error.code === 'ECONNABORTED' ||
          (error.response.status >= 500 && error.response.status < 600) ||
          error.response.status === 429 // Rate limiting
        )
      },
    }

    this.setupInterceptors()
  }

  private setupInterceptors(): void {
    // Request interceptor - add auth token, request ID, and metadata
    this.client.interceptors.request.use(
      (config) => {
        const token = this.getAccessToken()
        if (token) {
          config.headers.Authorization = `Bearer ${token}`
        }

        // Add request ID for tracking
        const requestId = this.generateRequestId()
        config.headers['X-Request-ID'] = requestId

        // Add request metadata
        const metadata: RequestMetadata = {
          startTime: Date.now(),
          retryCount: 0,
          endpoint: config.url || '',
          method: config.method?.toUpperCase() || 'GET',
        }
        config.metadata = metadata

        this.logRequest(config)
        return config
      },
      (error) => {
        console.error('Request setup error:', error)
        return Promise.reject(error)
      }
    )

    // Response interceptor - handle errors, token refresh, and retry logic
    this.client.interceptors.response.use(
      (response) => {
        this.logResponse(response)
        return response
      },
      async (error: AxiosError<ApiError>) => {
        const originalRequest = error.config as AxiosRequestConfig & {
          _retry?: boolean
          _retryCount?: number
          metadata?: RequestMetadata
        }

        // Handle 401 Unauthorized - try to refresh token
        if (error.response?.status === 401 && !originalRequest._retry) {
          originalRequest._retry = true

          try {
            // Prevent multiple refresh requests
            if (!this.refreshPromise) {
              this.refreshPromise = this.refreshAccessToken()
            }

            const tokens = await this.refreshPromise
            this.refreshPromise = null

            // Retry original request with new token
            originalRequest.headers = originalRequest.headers || {}
            originalRequest.headers.Authorization = `Bearer ${tokens.accessToken}`
            return this.client(originalRequest)
          } catch (refreshError) {
            // Refresh failed - logout user
            this.logout()
            if (typeof window !== 'undefined') {
              window.location.href = '/auth/signin'
            }
            return Promise.reject(refreshError)
          }
        }

        // Handle retry logic
        const retryCount = originalRequest._retryCount || 0
        if (
          retryCount < this.defaultRetryConfig.maxRetries &&
          this.defaultRetryConfig.retryCondition(error)
        ) {
          originalRequest._retryCount = retryCount + 1

          // Calculate delay with exponential backoff
          const delay = Math.min(
            this.defaultRetryConfig.baseDelay * 2 ** retryCount,
            this.defaultRetryConfig.maxDelay
          )

          await this.delay(delay)
          return this.client(originalRequest)
        }

        // Handle and categorize error
        const categorizedError = this.categorizeError(error)
        this.handleApiError(categorizedError)
        this.logError(error)

        return Promise.reject(categorizedError)
      }
    )
  }

  private categorizeError(error: AxiosError<ApiError>): CategorizedError {
    let category: ErrorCategory
    let retryable = false
    let userMessage: string

    if (error.response) {
      const { status, data } = error.response
      const serverMessage = data?.message || 'An error occurred'

      switch (status) {
        case 400:
          category = ErrorCategory.VALIDATION
          userMessage = `Invalid request: ${serverMessage}`
          break
        case 401:
          category = ErrorCategory.AUTHENTICATION
          userMessage = 'Please sign in to continue'
          break
        case 403:
          category = ErrorCategory.AUTHORIZATION
          userMessage = "You don't have permission to perform this action"
          break
        case 404:
          category = ErrorCategory.VALIDATION
          userMessage = 'The requested resource was not found'
          break
        case 429:
          category = ErrorCategory.SERVER
          userMessage = 'Too many requests. Please try again later.'
          retryable = true
          break
        case 500:
        case 502:
        case 503:
        case 504:
          category = ErrorCategory.SERVER
          userMessage = 'Server error. Please try again later.'
          retryable = true
          break
        default:
          category = ErrorCategory.UNKNOWN
          userMessage = serverMessage
      }
    } else if (error.request) {
      category = ErrorCategory.NETWORK
      userMessage = 'Network error. Please check your connection.'
      retryable = true
    } else if (error.code === 'ECONNABORTED') {
      category = ErrorCategory.TIMEOUT
      userMessage = 'Request timeout. Please try again.'
      retryable = true
    } else {
      category = ErrorCategory.UNKNOWN
      userMessage = 'An unexpected error occurred.'
    }

    return {
      ...error.response?.data,
      message: error.response?.data?.message || error.message,
      statusCode: error.response?.status || 0,
      category,
      retryable,
      userMessage,
      timestamp: new Date().toISOString(),
      path: error.config?.url,
      traceId: error.config?.headers?.['X-Request-ID'] as string,
    }
  }

  private handleApiError(error: CategorizedError): void {
    // Only show toast for non-authentication errors to avoid spam
    if (error.category !== ErrorCategory.AUTHENTICATION) {
      toast.error(error.userMessage)
    }
  }

  private generateRequestId(): string {
    return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  private delay(ms: number): Promise<void> {
    return new Promise((resolve) => setTimeout(resolve, ms))
  }

  private logRequest(config: AxiosRequestConfig): void {
    if (import.meta.env.DEV) {
      console.log(`[API] ${config.method?.toUpperCase()} ${config.url}`, {
        headers: config.headers,
        data: config.data,
      })
    }
  }

  private logResponse(response: AxiosResponse): void {
    if (import.meta.env.DEV) {
      const duration = Date.now() - (response.config.metadata?.startTime || 0)
      console.log(
        `[API] ${response.status} ${response.config.method?.toUpperCase()} ${response.config.url} (${duration}ms)`
      )
    }
  }

  private logError(error: AxiosError): void {
    if (import.meta.env.DEV) {
      console.error(`[API] Error ${error.config?.method?.toUpperCase()} ${error.config?.url}`, {
        status: error.response?.status,
        message: error.message,
        data: error.response?.data,
      })
    }
  }

  // Token management
  public getAccessToken(): string | null {
    if (typeof window !== 'undefined') {
      return localStorage.getItem(ACCESS_TOKEN_KEY)
    }
    return null
  }

  public getRefreshToken(): string | null {
    if (typeof window !== 'undefined') {
      return localStorage.getItem(REFRESH_TOKEN_KEY)
    }
    return null
  }

  public setTokens(tokens: TokenPair): void {
    if (typeof window !== 'undefined') {
      localStorage.setItem(ACCESS_TOKEN_KEY, tokens.accessToken)
      localStorage.setItem(REFRESH_TOKEN_KEY, tokens.refreshToken)
    }
  }

  public clearTokens(): void {
    if (typeof window !== 'undefined') {
      localStorage.removeItem(ACCESS_TOKEN_KEY)
      localStorage.removeItem(REFRESH_TOKEN_KEY)
      localStorage.removeItem(USER_KEY)
    }
  }

  // User management
  public getUser(): User | null {
    if (typeof window !== 'undefined') {
      const userStr = localStorage.getItem(USER_KEY)
      return userStr ? JSON.parse(userStr) : null
    }
    return null
  }

  public setUser(user: User): void {
    if (typeof window !== 'undefined') {
      localStorage.setItem(USER_KEY, JSON.stringify(user))
    }
  }

  // Auth methods
  private async refreshAccessToken(): Promise<TokenPair> {
    const refreshToken = this.getRefreshToken()
    if (!refreshToken) {
      throw new Error('No refresh token available')
    }

    const response = await axios.post<TokenPair>(`${API_BASE_URL}/auth/refresh`, {
      refreshToken,
    })

    const tokens = response.data
    this.setTokens(tokens)
    return tokens
  }

  public logout(): void {
    this.clearTokens()
  }

  // Generic request methods with enhanced error handling
  public async get<T = any>(url: string, config?: AxiosRequestConfig): Promise<AxiosResponse<T>> {
    try {
      return await this.client.get<T>(url, config)
    } catch (error) {
      throw this.enhanceError(error as AxiosError, 'GET', url)
    }
  }

  public async post<T = any>(
    url: string,
    data?: any,
    config?: AxiosRequestConfig
  ): Promise<AxiosResponse<T>> {
    try {
      return await this.client.post<T>(url, data, config)
    } catch (error) {
      throw this.enhanceError(error as AxiosError, 'POST', url)
    }
  }

  public async put<T = any>(
    url: string,
    data?: any,
    config?: AxiosRequestConfig
  ): Promise<AxiosResponse<T>> {
    try {
      return await this.client.put<T>(url, data, config)
    } catch (error) {
      throw this.enhanceError(error as AxiosError, 'PUT', url)
    }
  }

  public async patch<T = any>(
    url: string,
    data?: any,
    config?: AxiosRequestConfig
  ): Promise<AxiosResponse<T>> {
    try {
      return await this.client.patch<T>(url, data, config)
    } catch (error) {
      throw this.enhanceError(error as AxiosError, 'PATCH', url)
    }
  }

  public async delete<T = any>(
    url: string,
    config?: AxiosRequestConfig
  ): Promise<AxiosResponse<T>> {
    try {
      return await this.client.delete<T>(url, config)
    } catch (error) {
      throw this.enhanceError(error as AxiosError, 'DELETE', url)
    }
  }

  private enhanceError(error: AxiosError, method: string, url: string): CategorizedError {
    if (error.name === 'CategorizedError') {
      return error as unknown as CategorizedError
    }
    return this.categorizeError(error as AxiosError<ApiError>)
  }

  // File upload method with enhanced error handling
  public async upload<T = any>(
    url: string,
    formData: FormData,
    onProgress?: (progress: number) => void,
    config?: AxiosRequestConfig
  ): Promise<AxiosResponse<T>> {
    try {
      return await this.client.post<T>(url, formData, {
        ...config,
        headers: {
          'Content-Type': 'multipart/form-data',
          ...config?.headers,
        },
        timeout: 120000, // 2 minutes for file uploads
        onUploadProgress: (progressEvent) => {
          if (onProgress && progressEvent.total) {
            const progress = Math.round((progressEvent.loaded * 100) / progressEvent.total)
            onProgress(progress)
          }
        },
      })
    } catch (error) {
      throw this.enhanceError(error as AxiosError, 'POST', url)
    }
  }

  // Health check method
  public async healthCheck(): Promise<boolean> {
    try {
      const response = await this.client.get('/health')
      return response.status === 200
    } catch (error) {
      console.warn('Health check failed:', error)
      return false
    }
  }

  // Configuration methods
  public setTimeout(timeout: number): void {
    this.client.defaults.timeout = timeout
  }

  public setRetryConfig(config: Partial<RetryConfig>): void {
    this.defaultRetryConfig = { ...this.defaultRetryConfig, ...config }
  }

  public getBaseURL(): string {
    return this.client.defaults.baseURL || ''
  }

  public setBaseURL(baseURL: string): void {
    this.client.defaults.baseURL = baseURL
  }
}

// Export singleton instance
export const apiClient = new ApiClient()

// Export types
export type { ApiClient }

// Extend AxiosRequestConfig to include metadata
declare module 'axios' {
  interface AxiosRequestConfig {
    metadata?: RequestMetadata
    _retry?: boolean
    _retryCount?: number
  }
}
