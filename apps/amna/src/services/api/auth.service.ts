import { apiClient } from '~/api/client'

export interface LoginCredentials {
  email: string
  password: string
}

export interface LoginResponse {
  accessToken: string
  refreshToken: string
  user: {
    id: string
    email: string
    firstName: string
    lastName: string
    roles: string[]
    permissions: string[]
  }
}

export interface RegisterData {
  email: string
  password: string
  firstName: string
  lastName: string
  organizationId?: string
}

export interface RefreshTokenResponse {
  accessToken: string
  refreshToken: string
}

export class AuthService {
  private static readonly BASE_PATH = '/auth'

  static async login(credentials: LoginCredentials): Promise<LoginResponse> {
    const response = await apiClient.post<LoginResponse>(
      `${AuthService.BASE_PATH}/login`,
      credentials,
      { skipAuth: true }
    )

    // Store tokens
    apiClient.setAuthToken(response.accessToken)
    localStorage.setItem('refresh_token', response.refreshToken)

    return response
  }

  static async register(data: RegisterData): Promise<LoginResponse> {
    const response = await apiClient.post<LoginResponse>(
      `${AuthService.BASE_PATH}/register`,
      data,
      { skipAuth: true }
    )

    // Store tokens
    apiClient.setAuthToken(response.accessToken)
    localStorage.setItem('refresh_token', response.refreshToken)

    return response
  }

  static async logout(): Promise<void> {
    try {
      await apiClient.post(`${AuthService.BASE_PATH}/logout`)
    } finally {
      // Clear tokens regardless of API response
      apiClient.setAuthToken(null)
      localStorage.removeItem('refresh_token')
    }
  }

  static async refreshToken(): Promise<RefreshTokenResponse> {
    const refreshToken = localStorage.getItem('refresh_token')
    if (!refreshToken) {
      throw new Error('No refresh token available')
    }

    const response = await apiClient.post<RefreshTokenResponse>(
      `${AuthService.BASE_PATH}/refresh`,
      { refreshToken },
      { skipAuth: true }
    )

    // Update tokens
    apiClient.setAuthToken(response.accessToken)
    localStorage.setItem('refresh_token', response.refreshToken)

    return response
  }

  static async getCurrentUser() {
    return apiClient.get<LoginResponse['user']>(`${AuthService.BASE_PATH}/me`)
  }

  static async updateProfile(data: Partial<RegisterData>) {
    // TODO: Implement profile update when Command Center supports it
    // The Command Center currently doesn't have a PATCH endpoint for profile updates
    throw new Error('Profile update not yet implemented')
  }

  static isAuthenticated(): boolean {
    return !!apiClient.getAuthToken()
  }
}
