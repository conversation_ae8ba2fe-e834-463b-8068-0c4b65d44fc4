// Auth service integrating with @luminar/shared-auth
import { AuthClient } from '@luminar/shared-auth'
import { apiClient } from '~/api/client'
import type { LoginResponse, RegisterResponse } from '~/api/services/authService'

const TOKEN_KEY = 'auth_token'
const REFRESH_TOKEN_KEY = 'refresh_token'
const USER_KEY = 'current_user'

export interface User {
  id: string
  email: string
  name: string
  role: string
  permissions?: string[]
}

export interface AuthTokens {
  accessToken: string
  refreshToken: string
  expiresIn: number
}

// Create shared auth client instance
const sharedAuthClient = new AuthClient({
  apiBaseUrl: 'http://localhost:3000/api', // This should match the backend's base URL
  authEndpoint: '/auth', // This is the auth endpoint path
  tokenKey: TOKEN_KEY,
  refreshTokenKey: REFRESH_TOKEN_KEY,
  userKey: USER_KEY,
  enableAutoRefresh: true,
  storage: 'localStorage',
  onAuthError: (error) => {
    console.error('Auth error:', error)
    if (error.code === 'SESSION_EXPIRED') {
      clearAuthData()
      window.location.href = '/login'
    }
  },
  onTokenRefresh: (tokens) => {
    setAuthToken(tokens.accessToken)
    setRefreshToken(tokens.refreshToken)
    authEvents.emitTokenRefresh()
  },
})

// Token management
export const getAuthToken = (): string | null => {
  if (typeof window === 'undefined') return null
  return localStorage.getItem(TOKEN_KEY)
}

export const setAuthToken = (token: string): void => {
  if (typeof window === 'undefined') return
  localStorage.setItem(TOKEN_KEY, token)
  // Update the API client with the new token
  apiClient.setDefaultHeaders({ Authorization: `Bearer ${token}` })
}

export const clearAuthToken = (): void => {
  if (typeof window === 'undefined') return
  localStorage.removeItem(TOKEN_KEY)
  localStorage.removeItem(REFRESH_TOKEN_KEY)
  localStorage.removeItem(USER_KEY)
  // Clear auth header from API client
  apiClient.setDefaultHeaders({ Authorization: undefined })
}

// Refresh token management
export const getRefreshToken = (): string | null => {
  if (typeof window === 'undefined') return null
  return localStorage.getItem(REFRESH_TOKEN_KEY)
}

export const setRefreshToken = (token: string): void => {
  if (typeof window === 'undefined') return
  localStorage.setItem(REFRESH_TOKEN_KEY, token)
}

// User management
export const getCurrentUser = (): User | null => {
  if (typeof window === 'undefined') return null
  const userStr = localStorage.getItem(USER_KEY)
  if (!userStr) return null

  try {
    return JSON.parse(userStr)
  } catch {
    return null
  }
}

export const setCurrentUser = (user: User): void => {
  if (typeof window === 'undefined') return
  localStorage.setItem(USER_KEY, JSON.stringify(user))
}

// Auth state
export const isAuthenticated = (): boolean => {
  const token = getAuthToken()
  return !!token
}

// Store auth data
export const storeAuthData = (tokens: AuthTokens, user: User): void => {
  setAuthToken(tokens.accessToken)
  setRefreshToken(tokens.refreshToken)
  setCurrentUser(user)
  authEvents.emitLogin(user)
}

// Clear all auth data
export const clearAuthData = (): void => {
  clearAuthToken()
  authEvents.emitLogout()
}

// Token expiration check
export const isTokenExpired = (token: string): boolean => {
  try {
    const payload = JSON.parse(atob(token.split('.')[1]))
    const exp = payload.exp * 1000 // Convert to milliseconds
    return Date.now() > exp
  } catch {
    return true
  }
}

// Get token expiration time
export const getTokenExpiration = (token: string): Date | null => {
  try {
    const payload = JSON.parse(atob(token.split('.')[1]))
    return new Date(payload.exp * 1000)
  } catch {
    return null
  }
}

// Check if token needs refresh (5 minutes before expiration)
export const shouldRefreshToken = (): boolean => {
  const token = getAuthToken()
  if (!token) return false

  const expiration = getTokenExpiration(token)
  if (!expiration) return false

  const fiveMinutesFromNow = new Date(Date.now() + 5 * 60 * 1000)
  return expiration <= fiveMinutesFromNow
}

// Permission checking
export const hasPermission = (permission: string): boolean => {
  const user = getCurrentUser()
  if (!user || !user.permissions) return false
  return user.permissions.includes(permission)
}

// Role checking
export const hasRole = (role: string): boolean => {
  const user = getCurrentUser()
  if (!user) return false
  return user.role === role
}

// Multiple role checking
export const hasAnyRole = (roles: string[]): boolean => {
  const user = getCurrentUser()
  if (!user) return false
  return roles.includes(user.role)
}

// Auth event emitter for cross-component communication
export class AuthEventEmitter extends EventTarget {
  private static instance: AuthEventEmitter

  static getInstance(): AuthEventEmitter {
    if (!AuthEventEmitter.instance) {
      AuthEventEmitter.instance = new AuthEventEmitter()
    }
    return AuthEventEmitter.instance
  }

  emitLogin(user: User): void {
    this.dispatchEvent(new CustomEvent('login', { detail: user }))
  }

  emitLogout(): void {
    this.dispatchEvent(new CustomEvent('logout'))
  }

  emitTokenRefresh(): void {
    this.dispatchEvent(new CustomEvent('tokenRefresh'))
  }
}

export const authEvents = AuthEventEmitter.getInstance()

// Main auth service integrating with shared auth
export const authService = {
  async login(email: string, password: string, rememberMe?: boolean): Promise<LoginResponse> {
    const response = await sharedAuthClient.login({ email, password, rememberMe })
    const loginData = response.data as LoginResponse

    // Store auth data
    storeAuthData({
      accessToken: loginData.session.token,
      refreshToken: loginData.session.refreshToken,
      expiresIn: 3600000, // Default to 1 hour
    }, loginData.user)

    return loginData
  },

  async register(email: string, password: string, name: string): Promise<RegisterResponse> {
    const response = await sharedAuthClient.register({
      email,
      password,
      name,
      acceptTerms: true // Required by the API
    })
    const registerData = response.data as RegisterResponse

    // Store auth data
    storeAuthData({
      accessToken: registerData.session.token,
      refreshToken: registerData.session.refreshToken,
      expiresIn: 3600000, // Default to 1 hour
    }, registerData.user)

    return registerData
  },

  async logout(): Promise<void> {
    try {
      await sharedAuthClient.logout()
    } finally {
      clearAuthData()
    }
  },

  async refreshToken(): Promise<{ token: string; refreshToken: string }> {
    const currentRefreshToken = getRefreshToken()
    if (!currentRefreshToken) {
      throw new Error('No refresh token available')
    }

    const response = await sharedAuthClient.refreshToken(currentRefreshToken)
    const data = response.data as { token: string; refreshToken: string }

    // Update stored tokens
    setAuthToken(data.token)
    setRefreshToken(data.refreshToken)
    authEvents.emitTokenRefresh()

    return data
  },

  async validateSession(): Promise<{ valid: boolean; user?: LoginResponse['user'] }> {
    const response = await sharedAuthClient.validateSession()
    const data = response.data as { valid: boolean; user?: LoginResponse['user'] }

    if (data.valid && data.user) {
      setCurrentUser(data.user)
    } else {
      clearAuthData()
    }

    return data
  },

  async forgotPassword(email: string): Promise<{ message: string }> {
    const response = await sharedAuthClient.forgotPassword(email)
    return response.data as { message: string }
  },

  async resetPassword(token: string, newPassword: string): Promise<{ message: string }> {
    const response = await sharedAuthClient.resetPassword(token, newPassword)
    return response.data as { message: string }
  },

  // Re-export the shared auth client for advanced usage
  client: sharedAuthClient,
}
