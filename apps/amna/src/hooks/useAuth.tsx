import { useNavigate } from '@tanstack/react-router'
import React, { createContext, type ReactNode, useCallback, useContext, useEffect, useState } from 'react'
import { getAuthToken } from '~/api/client'
import { authService, getCurrentUser, type User } from '~/services/auth/auth.service'
import type { LoginResponse } from '~/api/services/authService'

interface LoginCredentials {
  email: string
  password: string
  rememberMe?: boolean
}

interface AuthContextType {
  user: User | null
  isAuthenticated: boolean
  isLoading: boolean
  login: (credentials: LoginCredentials) => Promise<void>
  logout: () => Promise<void>
  refreshAuth: () => Promise<void>
  error: string | null
}

const AuthContext = createContext<AuthContextType | undefined>(undefined)

interface AuthProviderProps {
  children: ReactNode
}

export function AuthProvider({ children }: AuthProviderProps) {
  const [user, setUser] = useState<User | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const navigate = useNavigate()

  // Initialize auth state from stored token
  useEffect(() => {
    const initAuth = async () => {
      const token = getAuthToken()
      if (token) {
        try {
          // Validate the session with the server
          const { valid, user: validatedUser } = await authService.validateSession()
          if (valid && validatedUser) {
            setUser(validatedUser as User)
          } else {
            // Try to refresh the token
            try {
              await authService.refreshToken()
              const currentUser = getCurrentUser()
              setUser(currentUser)
            } catch (refreshErr) {
              // Refresh failed, user will need to login again
              console.error('Token refresh failed:', refreshErr)
            }
          }
        } catch (err) {
          console.error('Session validation failed:', err)
        }
      }
      setIsLoading(false)
    }

    initAuth()
  }, [])

  const login = useCallback(async (credentials: LoginCredentials) => {
    setError(null)
    try {
      const response = await authService.login(credentials.email, credentials.password, credentials.rememberMe)
      setUser(response.user as User)
      navigate('/dashboard')
    } catch (err: any) {
      const errorMessage = err.message || 'Login failed'
      setError(errorMessage)
      throw err
    }
  }, [navigate])

  const logout = useCallback(async () => {
    try {
      await authService.logout()
    } catch (err) {
      // Even if logout API fails, clear local state
      console.error('Logout API failed:', err)
    } finally {
      setUser(null)
      navigate('/login')
    }
  }, [navigate])

  const refreshAuth = useCallback(async () => {
    try {
      await authService.refreshToken()
      const currentUser = getCurrentUser()
      setUser(currentUser)
    } catch (err) {
      setUser(null)
      throw err
    }
  }, [])

  // Set up automatic token refresh
  useEffect(() => {
    const refreshInterval = setInterval(async () => {
      if (user) {
        try {
          await refreshAuth()
        } catch (err) {
          console.error('Token refresh failed:', err)
        }
      }
    }, 10 * 60 * 1000) // Refresh every 10 minutes

    return () => clearInterval(refreshInterval)
  }, [user, refreshAuth])

  const value: AuthContextType = {
    user,
    isAuthenticated: !!user,
    isLoading,
    login,
    logout,
    refreshAuth,
    error
  }

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>
}

export function useAuth() {
  const context = useContext(AuthContext)
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
}

// Guard component for protected routes
interface RequireAuthProps {
  children: ReactNode
  requiredRoles?: string[]
  requiredPermissions?: string[]
}

export function RequireAuth({ children, requiredRoles, requiredPermissions }: RequireAuthProps) {
  const { user, isAuthenticated, isLoading } = useAuth()
  const navigate = useNavigate()

  useEffect(() => {
    if (!isLoading && !isAuthenticated) {
      navigate('/login', { replace: true })
    }
  }, [isAuthenticated, isLoading, navigate])

  if (isLoading) {
    return <div>Loading...</div>
  }

  if (!isAuthenticated || !user) {
    return null
  }

  // Check role requirements
  if (requiredRoles && requiredRoles.length > 0) {
    const hasRequiredRole = requiredRoles.includes(user.role)
    if (!hasRequiredRole) {
      return <div>Access Denied: Insufficient role privileges</div>
    }
  }

  // Check permission requirements
  if (requiredPermissions && requiredPermissions.length > 0) {
    const userPermissions = user.permissions || []
    const hasRequiredPermissions = requiredPermissions.every(perm => userPermissions.includes(perm))
    if (!hasRequiredPermissions) {
      return <div>Access Denied: Insufficient permissions</div>
    }
  }

  return <>{children}</>
}