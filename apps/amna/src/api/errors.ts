import { API_ERROR_CODES } from '~/config/api.config'

export type ApiErrorCode = (typeof API_ERROR_CODES)[keyof typeof API_ERROR_CODES]

export class ApiError extends Error {
  constructor(
    message: string,
    public code: ApiErrorCode,
    public statusCode?: number,
    public details?: unknown
  ) {
    super(message)
    this.name = 'ApiError'
    Object.setPrototypeOf(this, ApiError.prototype)
  }

  static fromResponse(response: Response, data?: unknown): ApiError {
    let code: ApiErrorCode = API_ERROR_CODES.SERVER_ERROR
    let message = 'An unexpected error occurred'

    switch (response.status) {
      case 400:
        code = API_ERROR_CODES.VALIDATION_ERROR
        message = 'Invalid request data'
        break
      case 401:
        code = API_ERROR_CODES.UNAUTHORIZED
        message = 'Authentication required'
        break
      case 403:
        code = API_ERROR_CODES.FORBIDDEN
        message = 'Access forbidden'
        break
      case 404:
        code = API_ERROR_CODES.NOT_FOUND
        message = 'Resource not found'
        break
      case 429:
        code = API_ERROR_CODES.RATE_LIMITED
        message = 'Too many requests'
        break
      case 500:
      case 502:
      case 503:
      case 504:
        code = API_ERROR_CODES.SERVER_ERROR
        message = 'Server error'
        break
    }

    // Override with server-provided error message if available
    if (data && typeof data === 'object' && 'message' in data) {
      message = String(data.message)
    }

    return new ApiError(message, code, response.status, data)
  }

  static networkError(error: Error): ApiError {
    return new ApiError(
      'Network connection failed',
      API_ERROR_CODES.NETWORK_ERROR,
      undefined,
      error
    )
  }

  static timeout(): ApiError {
    return new ApiError('Request timed out', API_ERROR_CODES.TIMEOUT, undefined)
  }

  static fromError(error: unknown): ApiError {
    if (error instanceof ApiError) {
      return error
    }

    if (error instanceof Error && 'response' in error) {
      // Handle axios-like errors
      const axiosError = error as any
      if (axiosError.response) {
        return ApiError.fromResponse(axiosError.response, axiosError.response.data)
      }
    }

    if (error instanceof Error) {
      return ApiError.networkError(error)
    }

    return new ApiError(
      'An unexpected error occurred',
      API_ERROR_CODES.SERVER_ERROR,
      undefined,
      error
    )
  }

  isRetryable(): boolean {
    return [
      API_ERROR_CODES.NETWORK_ERROR,
      API_ERROR_CODES.TIMEOUT,
      API_ERROR_CODES.SERVER_ERROR,
    ].includes(this.code)
  }

  toJSON() {
    return {
      code: this.code,
      message: this.message,
      statusCode: this.statusCode,
      details: this.details,
    }
  }
}

export function isApiError(error: unknown): error is ApiError {
  return error instanceof ApiError
}

export function handleApiError(error: unknown): ApiError {
  if (isApiError(error)) {
    return error
  }

  if (error instanceof Error) {
    return ApiError.networkError(error)
  }

  return new ApiError(
    'An unexpected error occurred',
    API_ERROR_CODES.SERVER_ERROR,
    undefined,
    error
  )
}
