import axios, { type AxiosError, type AxiosInstance, type AxiosRequestConfig } from 'axios'
import { API_CONFIG, HEADERS } from '~/config/api.config'
import { ApiError } from './errors'

export interface RequestConfig extends AxiosRequestConfig {
  skipAuth?: boolean
  retries?: number
  retryDelay?: number
}

class ApiClient {
  private instance: AxiosInstance
  private authToken: string | null = null

  constructor() {
    this.instance = axios.create({
      baseURL: API_CONFIG.baseURL,
      timeout: API_CONFIG.timeout,
      headers: HEADERS,
    })

    this.setupInterceptors()
  }

  private setupInterceptors() {
    // Request interceptor
    this.instance.interceptors.request.use(
      (config) => {
        // Add auth token if available and not skipped
        if (this.authToken && !config.skipAuth) {
          config.headers.Authorization = `Bearer ${this.authToken}`
        }

        // Add request timestamp for logging
        config.metadata = { startTime: Date.now() }

        return config
      },
      (error) => {
        return Promise.reject(error)
      }
    )

    // Response interceptor
    this.instance.interceptors.response.use(
      (response) => {
        // Log response time
        // const duration = Date.now() - response.config.metadata?.startTime
        // Debug logging removed for production

        return response
      },
      async (error: AxiosError) => {
        const originalRequest = error.config as RequestConfig

        // Handle network errors
        if (!error.response) {
          throw ApiError.networkError(error)
        }

        // Handle timeout
        if (error.code === 'ECONNABORTED') {
          throw ApiError.timeout()
        }

        // Extract error data
        const errorData = error.response.data
        const apiError = ApiError.fromResponse(error.response, errorData)

        // Retry logic for retryable errors
        if (apiError.isRetryable() && originalRequest) {
          const retries = originalRequest.retries ?? API_CONFIG.retries
          const currentRetry = originalRequest._retry || 0

          if (currentRetry < retries) {
            originalRequest._retry = currentRetry + 1
            const delay = originalRequest.retryDelay ?? API_CONFIG.retryDelay

            // Exponential backoff
            const backoffDelay = delay * 2 ** currentRetry

            // Debug logging removed for production

            await new Promise((resolve) => setTimeout(resolve, backoffDelay))

            return this.instance(originalRequest)
          }
        }

        throw apiError
      }
    )
  }

  setAuthToken(token: string | null) {
    this.authToken = token
    if (token) {
      // Store in localStorage for persistence
      localStorage.setItem('auth_token', token)
    } else {
      localStorage.removeItem('auth_token')
    }
  }

  getAuthToken(): string | null {
    if (!this.authToken) {
      // Try to restore from localStorage
      this.authToken = localStorage.getItem('auth_token')
    }
    return this.authToken
  }

  async get<T>(url: string, config?: RequestConfig) {
    const response = await this.instance.get<T>(url, config)
    return response.data
  }

  async post<T>(url: string, data?: unknown, config?: RequestConfig) {
    const response = await this.instance.post<T>(url, data, config)
    return response.data
  }

  async put<T>(url: string, data?: unknown, config?: RequestConfig) {
    const response = await this.instance.put<T>(url, data, config)
    return response.data
  }

  async patch<T>(url: string, data?: unknown, config?: RequestConfig) {
    const response = await this.instance.patch<T>(url, data, config)
    return response.data
  }

  async delete<T>(url: string, config?: RequestConfig) {
    const response = await this.instance.delete<T>(url, config)
    return response.data
  }

  // Upload file with progress tracking
  async upload<T>(
    url: string,
    file: File,
    onProgress?: (progress: number) => void,
    config?: RequestConfig
  ) {
    const formData = new FormData()
    formData.append('file', file)

    const response = await this.instance.post<T>(url, formData, {
      ...config,
      headers: {
        'Content-Type': 'multipart/form-data',
      },
      onUploadProgress: (progressEvent) => {
        if (onProgress && progressEvent.total) {
          const progress = Math.round((progressEvent.loaded * 100) / progressEvent.total)
          onProgress(progress)
        }
      },
    })

    return response.data
  }

  // Cancel request support
  createCancelToken() {
    return axios.CancelToken.source()
  }

  isCancel(error: unknown) {
    return axios.isCancel(error)
  }
}

// Export singleton instance
export const apiClient = new ApiClient()

// Export types
export type { ApiClient }

// Extend Axios config with metadata
declare module 'axios' {
  export interface AxiosRequestConfig {
    skipAuth?: boolean
    _retry?: number
    metadata?: {
      startTime: number
    }
  }
}
