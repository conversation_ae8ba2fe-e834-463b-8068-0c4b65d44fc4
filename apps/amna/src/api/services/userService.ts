import { ENDPOINTS } from '~/config/api.config'
import { apiClient } from '../client'
import type { ApiResponse, PaginatedResponse, User, UserPreferences, UserSession } from '../types'

export class UserService {
  /**
   * Get current user profile
   */
  async getProfile(): Promise<User> {
    const response = await apiClient.get<User>(ENDPOINTS.user.profile)
    return response.data
  }

  /**
   * Update user profile
   */
  async updateProfile(updates: Partial<User>): Promise<User> {
    const response = await apiClient.patch<User>(ENDPOINTS.user.profile, updates)
    return response.data
  }

  /**
   * Get user preferences
   */
  async getPreferences(): Promise<UserPreferences> {
    const response = await apiClient.get<UserPreferences>(ENDPOINTS.user.preferences)
    return response.data
  }

  /**
   * Update user preferences
   */
  async updatePreferences(preferences: Partial<UserPreferences>): Promise<UserPreferences> {
    const response = await apiClient.patch<UserPreferences>(
      ENDPOINTS.user.preferences,
      preferences
    )
    return response.data
  }

  /**
   * Get active sessions
   */
  async getSessions(): Promise<PaginatedResponse<UserSession>> {
    const response = await apiClient.get<PaginatedResponse<UserSession>>(ENDPOINTS.user.sessions)
    return response.data
  }

  /**
   * Revoke a session
   */
  async revokeSession(sessionId: string): Promise<void> {
    await apiClient.delete(`${ENDPOINTS.user.sessions}/${sessionId}`)
  }

  /**
   * Revoke all sessions except current
   */
  async revokeAllSessions(): Promise<void> {
    await apiClient.post(`${ENDPOINTS.user.sessions}/revoke-all`)
  }

  /**
   * Upload avatar
   */
  async uploadAvatar(file: File, onProgress?: (progress: number) => void): Promise<string> {
    const formData = new FormData()
    formData.append('file', file)
    
    const response = await apiClient.post<{ url: string }>(
      `${ENDPOINTS.user.profile}/avatar`,
      formData,
      {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
        onUploadProgress: onProgress ? (progressEvent) => {
          if (progressEvent.total) {
            onProgress(Math.round((progressEvent.loaded * 100) / progressEvent.total))
          }
        } : undefined,
      } as any
    )
    return response.data.url
  }

  /**
   * Delete user account
   */
  async deleteAccount(confirmation: string): Promise<void> {
    await apiClient.delete(ENDPOINTS.user.profile, {
      data: { confirmation },
    })
  }

  /**
   * Export user data
   */
  async exportData(format: 'json' | 'csv' = 'json'): Promise<Blob> {
    const response = await apiClient.get<Blob>(`${ENDPOINTS.user.profile}/export?format=${format}`, {
      headers: {
        'Accept': format === 'csv' ? 'text/csv' : 'application/json',
      },
    } as any)
    return response.data
  }

  /**
   * Change password
   */
  async changePassword(currentPassword: string, newPassword: string): Promise<void> {
    await apiClient.post(`${ENDPOINTS.user.profile}/change-password`, {
      currentPassword,
      newPassword,
    })
  }

  /**
   * Enable two-factor authentication
   */
  async enable2FA(): Promise<{
    secret: string
    qrCode: string
    backupCodes: string[]
  }> {
    const response = await apiClient.post<{
      secret: string
      qrCode: string
      backupCodes: string[]
    }>(`${ENDPOINTS.user.profile}/2fa/enable`)
    return response.data
  }

  /**
   * Disable two-factor authentication
   */
  async disable2FA(code: string): Promise<void> {
    await apiClient.post(`${ENDPOINTS.user.profile}/2fa/disable`, { code })
  }

  /**
   * Verify two-factor authentication code
   */
  async verify2FA(code: string): Promise<boolean> {
    const response = await apiClient.post<{ valid: boolean }>(
      `${ENDPOINTS.user.profile}/2fa/verify`,
      { code }
    )
    return response.data.valid
  }
}

// Export singleton instance
export const userService = new UserService()
